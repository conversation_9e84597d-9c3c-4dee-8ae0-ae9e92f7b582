"""
Simplified Email Agent using Mistral AI Agents API
Handles Gmail integration and email processing
"""

import asyncio
import logging
import base64
import json
import os
import email
import io
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

import httpx
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# PDF processing imports
try:
    import pymupdf as fitz  # PyMuPDF
    PDF_AVAILABLE = True
except ImportError:
    try:
        import PyPDF2
        PDF_AVAILABLE = True
    except ImportError:
        PDF_AVAILABLE = False

logger = logging.getLogger(__name__)


class EmailAgent:
    """Simplified Email Agent using Mistral AI."""
    
    def __init__(self, mistral_api_key: str, supabase, graphiti=None):
        """Initialize the email agent."""
        self.mistral_api_key = mistral_api_key
        self.supabase = supabase
        self.graphiti = graphiti
        self.gmail_service = None
        self.processed_emails = set()  # Track processed email IDs to prevent duplicates

        # Gmail configuration
        self.gmail_scopes = [
            'https://www.googleapis.com/auth/gmail.readonly',
            'https://www.googleapis.com/auth/gmail.modify'
        ]

        # Internal senders for shared inbox handling
        self.internal_senders = {
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        }

        logger.info("[EMAIL] Email Agent initialized")
    
    async def initialize_gmail(self):
        """Initialize Gmail API service."""
        try:
            import pickle
            creds = None

            # Load existing credentials from pickle file
            if os.path.exists('token.pickle'):
                with open('token.pickle', 'rb') as token:
                    creds = pickle.load(token)

            # If no valid credentials, get new ones
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    if not os.path.exists('credentials.json'):
                        raise Exception("Gmail credentials.json not found")

                    flow = InstalledAppFlow.from_client_secrets_file(
                        'credentials.json', self.gmail_scopes
                    )
                    creds = flow.run_local_server(port=8080)

                # Save credentials to pickle file
                with open('token.pickle', 'wb') as token:
                    pickle.dump(creds, token)

            self.gmail_service = build('gmail', 'v1', credentials=creds)
            logger.info("[SUCCESS] Gmail API initialized")

        except Exception as e:
            logger.error(f"[ERROR] Gmail initialization failed: {e}")
            raise
    
    async def check_emails(self):
        """Check for new emails and process them."""
        try:
            if not self.gmail_service:
                await self.initialize_gmail()
            
            # Get unread emails
            results = self.gmail_service.users().messages().list(
                userId='me',
                q='is:unread',
                maxResults=10
            ).execute()
            
            messages = results.get('messages', [])
            
            if not messages:
                logger.debug("No new emails found")
                return
            
            logger.info(f"[EMAIL] Found {len(messages)} new emails")

            # Process each email (skip already processed ones)
            for message in messages:
                email_id = message['id']
                if email_id not in self.processed_emails:
                    await self._process_email(email_id)
                    self.processed_emails.add(email_id)
                else:
                    logger.debug(f"[EMAIL] Skipping already processed email {email_id}")
                
        except Exception as e:
            logger.error(f"❌ Error checking emails: {e}")
    
    async def _process_email(self, message_id: str):
        """Process a single email."""
        try:
            # Get email details
            message = self.gmail_service.users().messages().get(
                userId='me',
                id=message_id
            ).execute()
            
            # Extract email data
            email_data = self._extract_email_data(message)

            # Check if internal sender (shared inbox handling)
            sender_email = email_data.get('from', '').lower()
            is_internal = any(internal in sender_email for internal in self.internal_senders)
            email_data['is_internal'] = is_internal

            if is_internal:
                logger.info(f"[INTERNAL] Processing email from internal sender: {sender_email}")

            # Get email thread for context (especially important for internal emails)
            thread_context = self._get_email_thread_context(message)
            if thread_context:
                email_data['thread_context'] = thread_context
                if is_internal:
                    logger.info(f"[INTERNAL] Retrieved thread context for internal email: {len(thread_context)} chars")

            # Process attachments (including PDFs)
            attachments = self._extract_attachments(message)
            if attachments:
                email_data['attachments'] = attachments
                # Add attachment content to email body for analysis
                attachment_text = self._process_attachments(attachments)
                if attachment_text:
                    email_data['body'] += f"\n\n--- ATTACHMENT CONTENT ---\n{attachment_text}"

            # Process with Mistral AI (including thread context)
            analysis = await self._analyze_email_with_mistral(email_data)

            # Check if human review is needed
            needs_review = self._needs_human_review(analysis, email_data)
            if needs_review:
                analysis['review_status'] = 'pending'
                analysis['review_reason'] = needs_review
                logger.warning(f"[REVIEW] Email from {email_data['from']} flagged for human review: {needs_review}")

                # Send notification for critical emails
                await self._send_review_notification(email_data, analysis)
            else:
                analysis['review_status'] = 'auto_approved'

            # Store in Supabase
            await self._store_email_interaction(email_data, analysis)

            # Update temporal knowledge
            if self.graphiti:
                await self._update_email_knowledge(email_data, analysis)
            
            # Mark as read
            self.gmail_service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            
            logger.info(f"[SUCCESS] Processed email from {email_data['from']}")
            
        except Exception as e:
            logger.error(f"[ERROR] Error processing email {message_id}: {e}")

    def _needs_human_review(self, analysis: Dict[str, Any], email_data: Dict[str, Any]) -> str:
        """Determine if email needs human review and return reason."""

        # Always flag internal emails for review (shared inbox handling)
        is_internal = email_data.get('is_internal', False)
        if is_internal:
            sender = email_data.get('from', 'unknown')
            return f"Internal sender (shared inbox): {sender}"

        # Check urgency level
        urgency = analysis.get('urgency', 'low').lower()
        if urgency in ['urgent', 'high']:
            return f"High urgency: {urgency}"

        # Check confidence level
        confidence = analysis.get('confidence', 1.0)
        if confidence < 0.7:
            return f"Low confidence: {confidence:.2f}"

        # Check for complaint indicators
        intent = analysis.get('intent', '').lower()
        if 'complaint' in intent or 'issue' in intent:
            return f"Potential complaint: {intent}"

        # Check for special flags
        flags = analysis.get('flags', [])
        if isinstance(flags, list):
            critical_flags = ['complaint', 'urgent', 'technical_issue', 'billing_issue']
            for flag in flags:
                if flag.lower() in critical_flags:
                    return f"Critical flag: {flag}"

        # Check for non-JSON responses (parsing errors)
        if analysis.get('needs_review', False):
            return "Non-structured response from AI"

        # Check for large attachments (might need manual review)
        attachments = email_data.get('attachments', [])
        if len(attachments) > 2:
            return f"Multiple attachments: {len(attachments)} files"

        # Check for thread complexity
        thread_context = email_data.get('thread_context', '')
        if thread_context and len(thread_context) > 500:
            return "Complex email thread"

        return ""  # No review needed

    def _extract_message_body(self, payload: Dict[str, Any]) -> str:
        """Extract message body from Gmail API payload."""
        try:
            import base64

            if 'parts' in payload:
                # Multi-part message
                for part in payload['parts']:
                    if part.get('mimeType') == 'text/plain':
                        data = part.get('body', {}).get('data', '')
                        if data:
                            return base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
                    elif part.get('mimeType') == 'text/html':
                        data = part.get('body', {}).get('data', '')
                        if data:
                            return base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
            else:
                # Single part message
                data = payload.get('body', {}).get('data', '')
                if data:
                    return base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')

            return ""

        except Exception as e:
            logger.warning(f"[WARNING] Error extracting message body: {e}")
            return ""

    async def _send_review_notification(self, email_data: Dict[str, Any], analysis: Dict[str, Any]):
        """Send notification for emails requiring human review."""
        try:
            # Import notification system
            from notification_system import notify_review_required
            await notify_review_required(email_data, analysis)
        except Exception as e:
            logger.error(f"[ERROR] Failed to send review notification: {e}")

    def _extract_attachments(self, message: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract attachments from Gmail message."""
        attachments = []

        try:
            payload = message.get('payload', {})
            parts = payload.get('parts', [])

            # Check if message has parts (multipart)
            if not parts:
                # Single part message, check if it has attachment
                if payload.get('filename'):
                    parts = [payload]

            for part in parts:
                filename = part.get('filename', '')
                if filename and part.get('body', {}).get('attachmentId'):
                    # Get attachment data
                    attachment_id = part['body']['attachmentId']
                    attachment_data = self.gmail_service.users().messages().attachments().get(
                        userId='me',
                        messageId=message['id'],
                        id=attachment_id
                    ).execute()

                    attachments.append({
                        'filename': filename,
                        'mime_type': part.get('mimeType', ''),
                        'size': part.get('body', {}).get('size', 0),
                        'data': attachment_data.get('data', ''),
                        'attachment_id': attachment_id
                    })

        except Exception as e:
            logger.error(f"[ERROR] Error extracting attachments: {e}")

        return attachments

    def _process_attachments(self, attachments: List[Dict[str, Any]]) -> str:
        """Process attachments and extract text content."""
        extracted_text = []

        for attachment in attachments:
            filename = attachment['filename']
            mime_type = attachment['mime_type']

            logger.info(f"[ATTACHMENT] Processing {filename} ({mime_type})")

            # Process PDF files
            if mime_type == 'application/pdf' or filename.lower().endswith('.pdf'):
                pdf_text = self._extract_pdf_text(attachment['data'])
                if pdf_text:
                    extracted_text.append(f"PDF Content from {filename}:\n{pdf_text}")

            # Process text files
            elif mime_type.startswith('text/') or filename.lower().endswith(('.txt', '.csv')):
                try:
                    # Decode base64 attachment data
                    text_data = base64.urlsafe_b64decode(attachment['data']).decode('utf-8')
                    extracted_text.append(f"Text Content from {filename}:\n{text_data}")
                    logger.info(f"[ATTACHMENT] Extracted text from {filename} ({len(text_data)} chars)")
                except Exception as e:
                    logger.error(f"[ERROR] Error processing text attachment {filename}: {e}")

            # Process image files (log for potential OCR implementation)
            elif mime_type.startswith('image/') or filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
                logger.info(f"[ATTACHMENT] Skipping image file: {filename} ({mime_type}) - OCR not implemented")
                # TODO: Implement OCR for image processing if needed
                # extracted_text.append(f"Image Content from {filename}: [OCR would extract text here]")

            else:
                logger.info(f"[ATTACHMENT] Skipping unsupported file type: {filename} ({mime_type}, {attachment.get('size', 0)} bytes)")

        return "\n\n".join(extracted_text)

    def _extract_pdf_text(self, pdf_data: str) -> str:
        """Extract text from PDF attachment."""
        if not PDF_AVAILABLE:
            logger.warning("[WARNING] PDF processing not available - install pymupdf or PyPDF2")
            return ""

        try:
            # Decode base64 PDF data
            pdf_bytes = base64.urlsafe_b64decode(pdf_data)

            # Try PyMuPDF first (more reliable)
            if 'fitz' in globals():
                doc = fitz.open(stream=pdf_bytes, filetype="pdf")
                text = ""
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    text += page.get_text("text")
                doc.close()
                return text.strip()

            # Fallback to PyPDF2
            elif 'PyPDF2' in globals():
                pdf_file = io.BytesIO(pdf_bytes)
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text()
                return text.strip()

        except Exception as e:
            logger.error(f"[ERROR] Error extracting PDF text: {e}")

        return ""

    def _get_email_thread_context(self, message: Dict[str, Any]) -> str:
        """Get email thread context for better analysis."""
        try:
            thread_id = message.get('threadId')
            if not thread_id or not self.gmail_service:
                return ""

            # Get thread messages
            thread = self.gmail_service.users().threads().get(
                userId='me',
                id=thread_id
            ).execute()

            messages = thread.get('messages', [])
            if len(messages) <= 1:
                return ""  # No thread context if only one message

            # Extract context from previous messages (limit to last 3 for brevity)
            thread_context = []
            current_msg_id = message.get('id', '')

            for msg in messages:
                # Skip the current message
                if msg.get('id') == current_msg_id:
                    continue

                try:
                    # Extract basic info from thread message
                    headers = msg.get('payload', {}).get('headers', [])
                    from_header = next((h['value'] for h in headers if h['name'].lower() == 'from'), 'Unknown')
                    subject_header = next((h['value'] for h in headers if h['name'].lower() == 'subject'), 'No subject')
                    date_header = next((h['value'] for h in headers if h['name'].lower() == 'date'), 'Unknown date')

                    # Get message body (simplified)
                    body = self._extract_message_body(msg.get('payload', {}))
                    body_preview = body[:200] + "..." if len(body) > 200 else body

                    thread_context.append(f"From: {from_header}\nSubject: {subject_header}\nDate: {date_header}\nContent: {body_preview}\n---")

                    # Limit to last 3 messages for performance
                    if len(thread_context) >= 3:
                        break

                except Exception as msg_error:
                    logger.warning(f"[WARNING] Error processing thread message: {msg_error}")
                    continue

            if thread_context:
                logger.info(f"[THREAD] Retrieved {len(thread_context)} messages for thread context")
                return "\n".join(thread_context)
            else:
                return ""

        except Exception as e:
            logger.error(f"[ERROR] Error getting thread context: {e}")
            return ""

    def _extract_email_data(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant data from Gmail message."""
        headers = message['payload'].get('headers', [])
        
        # Extract headers
        email_data = {
            'id': message['id'],
            'thread_id': message['threadId'],
            'from': '',
            'to': '',
            'subject': '',
            'date': '',
            'body': ''
        }
        
        for header in headers:
            name = header['name'].lower()
            if name == 'from':
                email_data['from'] = header['value']
            elif name == 'to':
                email_data['to'] = header['value']
            elif name == 'subject':
                email_data['subject'] = header['value']
            elif name == 'date':
                email_data['date'] = header['value']
        
        # Extract body
        email_data['body'] = self._extract_body(message['payload'])
        
        return email_data
    
    def _extract_body(self, payload: Dict[str, Any]) -> str:
        """Extract email body from payload."""
        body = ""
        
        if 'parts' in payload:
            for part in payload['parts']:
                if part['mimeType'] == 'text/plain':
                    data = part['body'].get('data', '')
                    if data:
                        body = base64.urlsafe_b64decode(data).decode('utf-8')
                        break
        else:
            if payload['mimeType'] == 'text/plain':
                data = payload['body'].get('data', '')
                if data:
                    body = base64.urlsafe_b64decode(data).decode('utf-8')
        
        return body
    
    async def _analyze_email_with_mistral(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze email using Mistral AI."""
        try:
            # Validate email data before processing
            email_from = email_data.get('from', 'Unknown sender')
            email_subject = email_data.get('subject', 'No subject')
            email_body = email_data.get('body', '')

            # Check for empty email body but don't skip if there are attachments
            has_attachments = 'attachments' in email_data and email_data['attachments']
            if not email_body or not email_body.strip():
                if not has_attachments:
                    logger.warning(f"[WARNING] Skipping email from {email_from} - empty body and no attachments")
                    return {
                        "intent": "unknown",
                        "urgency": "low",
                        "summary": "Email has no readable content and no attachments",
                        "error": "empty_body_no_attachments"
                    }
                else:
                    # Email has attachments but empty body - process attachments only
                    logger.info(f"[INFO] Processing email from {email_from} - empty body but has {len(email_data['attachments'])} attachments")
                    email_body = "[Email body is empty - content extracted from attachments]"

            # Truncate very long emails to avoid API limits
            if len(email_body) > 4000:
                email_body = email_body[:4000] + "... [truncated]"
                logger.info(f"[INFO] Truncated long email from {email_from}")

            # Include thread context if available
            thread_context = email_data.get('thread_context', '')
            thread_section = f"\n\nThread Context:\n{thread_context}" if thread_context else ""

            # Check if internal sender
            is_internal = email_data.get('is_internal', False)
            sender_type = "internal colleague" if is_internal else "customer"

            # Prepare enhanced prompt for Mistral with strict JSON requirements
            prompt = f"""
            You are an email analysis AI. Analyze this email and return ONLY a valid JSON object.

            Email Details:
            From: {email_from} ({'INTERNAL SENDER' if is_internal else 'EXTERNAL CUSTOMER'})
            Subject: {email_subject}
            Body: {email_body}{thread_section}

            {'IMPORTANT: This is from an internal colleague using a shared inbox. Focus on understanding if they are replying to a customer or handling internal business.' if is_internal else ''}

            Return ONLY this JSON structure (no other text):
            {{
                "intent": "order|inquiry|complaint|support|urgent_issue|internal_reply|other",
                "urgency": "low|medium|high|urgent",
                "confidence": 0.85,
                "topics": ["topic1", "topic2"],
                "entities": {{"order_number": "value", "customer_name": "value", "sku": "value"}},
                "thread_relevance": "description of how this relates to thread context",
                "summary": "brief summary of email content",
                "suggested_action": "respond|escalate|process_order|review_internal|other",
                "flags": ["complaint", "urgent", "technical_issue", "internal_communication"],
                "is_internal": {str(is_internal).lower()}
            }}

            CRITICAL: Respond with ONLY the JSON object above. No explanations, no markdown, no additional text.
            """

            # Check payload size and log for monitoring
            payload_size = len(prompt)
            logger.info(f"[MISTRAL] Sending {payload_size} characters to Mistral for analysis")

            # Implement aggressive truncation for very large payloads
            MAX_CHARS = 15000  # ~4k tokens, adjust based on model context window
            if payload_size > MAX_CHARS:
                logger.warning(f"[MISTRAL] Payload very large ({payload_size} chars). Truncating to {MAX_CHARS}")
                # Truncate the email body while preserving structure
                truncated_body = email_body[:MAX_CHARS - 1000] + "\n\n[EMAIL TRUNCATED DUE TO SIZE]"
                prompt = f"""
                Analyze this customer email and extract key information:

                From: {email_from}
                Subject: {email_subject}
                Body: {truncated_body}

                Please provide:
                1. Intent (inquiry, complaint, order, support, etc.)
                2. Urgency level (low, medium, high, urgent)
                3. Key topics mentioned
                4. Any product SKUs or order numbers mentioned
                5. Suggested response category
                6. Summary of the email

                Respond in JSON format.
                """
            
            # Call Mistral API with retry logic and extended timeout
            timeout = httpx.Timeout(30.0, connect=5.0)  # 30s read, 5s connect
            max_retries = 3
            retry_delay = 2  # seconds

            for attempt in range(max_retries):
                try:
                    async with httpx.AsyncClient(timeout=timeout) as client:
                        start_time = datetime.now()
                        response = await client.post(
                            "https://api.mistral.ai/v1/chat/completions",
                            headers={
                                "Authorization": f"Bearer {self.mistral_api_key}",
                                "Content-Type": "application/json"
                            },
                            json={
                                "model": "mistral-large-latest",
                                "messages": [
                                    {"role": "user", "content": prompt}
                                ],
                                "temperature": 0.3,
                                "max_tokens": 1000
                            }
                        )

                        # Log response time for monitoring
                        response_time = (datetime.now() - start_time).total_seconds()
                        logger.info(f"[MISTRAL] API response received in {response_time:.2f}s (attempt {attempt + 1})")

                        # Process response immediately while we have it
                        if response.status_code == 200:
                            result = response.json()

                            # Validate response structure
                            if not result or 'choices' not in result:
                                logger.error(f"[MISTRAL] Invalid response structure: {result}")
                                if attempt < max_retries - 1:
                                    continue  # Retry
                                else:
                                    return {"error": "invalid_response_structure", "needs_review": True}

                            choices = result.get('choices', [])
                            if not choices or 'message' not in choices[0]:
                                logger.error(f"[MISTRAL] No choices or message in response: {result}")
                                if attempt < max_retries - 1:
                                    continue  # Retry
                                else:
                                    return {"error": "no_message_content", "needs_review": True}

                            content = choices[0]['message'].get('content', '')
                            if not content:
                                logger.error(f"[MISTRAL] Empty content in response: {result}")
                                if attempt < max_retries - 1:
                                    continue  # Retry
                                else:
                                    return {"error": "empty_content", "needs_review": True}

                            # Log Mistral's full analysis for human review
                            logger.info(f"[MISTRAL] Analysis result: {content}")

                            # Save detailed analysis to file for human review
                            try:
                                with open("logs/mistral_analysis.log", "a", encoding='utf-8') as f:
                                    f.write(f"\n{'='*80}\n")
                                    f.write(f"Timestamp: {datetime.now(timezone.utc).isoformat()}\n")
                                    f.write(f"From: {email_data.get('from', 'unknown')}\n")
                                    f.write(f"Subject: {email_data.get('subject', 'unknown')}\n")
                                    f.write(f"Payload Size: {len(prompt)} chars\n")
                                    f.write(f"Response Time: {response_time:.2f}s\n")
                                    f.write(f"Analysis Result:\n{content}\n")
                                    f.write(f"{'='*80}\n")
                            except Exception as e:
                                logger.error(f"[ERROR] Failed to write analysis log: {e}")

                            # Try to parse JSON response (handle markdown code blocks)
                            try:
                                # Remove markdown code blocks if present
                                clean_content = content.strip()
                                if clean_content.startswith('```json'):
                                    clean_content = clean_content[7:]  # Remove ```json
                                if clean_content.startswith('```'):
                                    clean_content = clean_content[3:]   # Remove ```
                                if clean_content.endswith('```'):
                                    clean_content = clean_content[:-3]  # Remove trailing ```
                                clean_content = clean_content.strip()

                                analysis = json.loads(clean_content)
                                # Log structured analysis for easier review
                                logger.info(f"[MISTRAL] Structured analysis - Intent: {analysis.get('intent', 'unknown')}, "
                                          f"Urgency: {analysis.get('urgency', 'unknown')}, "
                                          f"Confidence: {analysis.get('confidence', 'unknown')}, "
                                          f"Summary: {analysis.get('summary', 'No summary')[:100]}...")
                                return analysis
                            except json.JSONDecodeError:
                                # Fallback if not valid JSON
                                logger.warning(f"[MISTRAL] Non-JSON response received: {content[:200]}...")
                                analysis = {
                                    "intent": "unknown",
                                    "urgency": "medium",
                                    "summary": content,
                                    "raw_response": content,
                                    "needs_review": True  # Flag for human review
                                }
                                return analysis
                        else:
                            logger.error(f"[MISTRAL] HTTP error {response.status_code}: {response.text}")
                            if attempt < max_retries - 1:
                                continue  # Retry
                            else:
                                return {"error": f"http_error_{response.status_code}", "needs_review": True}

                        break  # Success, exit retry loop

                except (httpx.ReadTimeout, httpx.ConnectTimeout) as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"[MISTRAL] Timeout on attempt {attempt + 1}/{max_retries}, retrying in {retry_delay}s...")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                    else:
                        logger.error(f"[MISTRAL] All {max_retries} attempts failed due to timeout")
                        return {"error": "timeout", "needs_review": True}
                except Exception as e:
                    logger.error(f"[MISTRAL] API error on attempt {attempt + 1}: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2
                    else:
                        logger.error(f"[MISTRAL] All attempts failed with error: {e}")
                        return {"error": str(e), "needs_review": True}

            # This should never be reached, but just in case
            return {"error": "unexpected_end_of_function", "needs_review": True}

        except Exception:
            logger.exception("[ERROR] Error analyzing email with Mistral")
            return {"error": "email_analysis_failed"}
    
    async def _store_email_interaction(self, email_data: Dict[str, Any], analysis: Dict[str, Any]):
        """Store email interaction in Supabase."""
        try:
            interaction_data = {
                "customer_email": email_data['from'],
                "interaction_type": "email",
                "content": f"Subject: {email_data['subject']}\n\n{email_data['body']}",
                "metadata": {
                    "email_id": email_data['id'],
                    "thread_id": email_data['thread_id'],
                    "subject": email_data['subject'],
                    "analysis": analysis
                },
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            self.supabase.table("customer_interactions").insert(interaction_data).execute()
            logger.debug(f"Stored email interaction for {email_data['from']}")
            
        except Exception as e:
            logger.error(f"❌ Failed to store email interaction: {e}")
    
    async def _update_email_knowledge(self, email_data: Dict[str, Any], analysis: Dict[str, Any]):
        """Update temporal knowledge in Graphiti."""
        try:
            if self.graphiti:
                memories = [
                    f"Customer {email_data['from']} sent email with subject: {email_data['subject']}",
                    f"Email intent: {analysis.get('intent', 'unknown')}",
                    f"Email urgency: {analysis.get('urgency', 'medium')}"
                ]
                
                await self.graphiti.add_episodic_memories(memories)
                logger.debug(f"Updated email knowledge for {email_data['from']}")
                
        except Exception as e:
            logger.error(f"❌ Failed to update email knowledge: {e}")
    
    async def send_response(self, to_email: str, subject: str, body: str):
        """Send email response."""
        try:
            if not self.gmail_service:
                await self.initialize_gmail()
            
            # Create message
            message = MIMEText(body)
            message['to'] = to_email
            message['subject'] = f"Re: {subject}"
            
            # Send email
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
            
            self.gmail_service.users().messages().send(
                userId='me',
                body={'raw': raw_message}
            ).execute()
            
            logger.info(f"✅ Sent response to {to_email}")
            
        except Exception as e:
            logger.error(f"❌ Failed to send email response: {e}")


# Add missing import
import os
